# 测试事件绑定功能

## 测试步骤

### 1. 基础功能测试

1. 打开浏览器访问 http://localhost:5174/
2. 在设计器中添加一个按钮组件
3. 选中按钮组件，在右侧属性面板找到"事件绑定"栏
4. 测试以下配置：

#### 测试用例1：基础按钮点击
- 启用通信：✅ 开启
- 通信命令名：`test_button_click`
- 通信参数：`{"action":"test","user":"admin"}`
- 通信触发方式：点击时触发

#### 测试用例2：输入框变化
1. 添加输入框组件
2. 配置事件绑定：
   - 启用通信：✅ 开启
   - 通信命令名：`input_value_change`
   - 通信参数：`{"field":"username"}`
   - 通信触发方式：值变化时触发

#### 测试用例3：表格数据
1. 添加表格组件
2. 配置事件绑定：
   - 启用通信：✅ 开启
   - 通信命令名：`table_selection`
   - 通信参数：`{"table":"user_list"}`
   - 通信触发方式：值变化时触发

### 2. 预览模式测试

1. 点击顶部工具栏的"预览"按钮
2. 在预览模式下测试各个组件的事件触发
3. 检查浏览器控制台是否输出正确的事件信息
4. 检查是否显示相应的消息提示

### 3. 导出功能测试

1. 点击"导出项目"按钮
2. 下载生成的项目ZIP文件
3. 解压并检查以下文件：
   - `src/communication.js` - 通信模块
   - 各个窗口组件文件中的事件绑定代码
   - 组件是否正确绑定了事件处理函数

### 4. 预期结果

#### 在开发环境中：
- 点击按钮时显示：`事件触发: test_button_click`
- 输入框变化时显示：`输入框事件: input_value_change`
- 控制台输出完整的事件数据结构

#### 在导出的项目中：
- 包含完整的通信模块
- 组件正确绑定事件处理函数
- 支持WebView2和传统WebBrowser通信

## 验证要点

### 1. 属性面板功能
- [ ] 事件绑定栏正确显示
- [ ] 启用开关正常工作
- [ ] 只有启用时才显示其他配置项
- [ ] 各个输入框可以正常输入和保存
- [ ] 触发方式下拉框显示正确选项

### 2. 组件事件绑定
- [ ] 按钮组件支持点击事件
- [ ] 输入框组件支持变化事件
- [ ] 选择框组件支持变化事件
- [ ] 表格组件支持行选择事件

### 3. 数据传递
- [ ] 命令名正确传递
- [ ] 参数JSON正确解析
- [ ] 组件ID正确传递
- [ ] 时间戳正确生成

### 4. 导出功能
- [ ] 通信模块文件正确生成
- [ ] 组件事件处理代码正确生成
- [ ] 支持多种通信方式
- [ ] 表格数据特殊处理正确

## 常见问题排查

### 问题1：事件绑定栏不显示
- 检查组件是否正确选中
- 检查PropertyPanel.vue是否正确更新

### 问题2：事件不触发
- 检查是否启用了通信
- 检查命令名是否填写
- 检查触发方式是否正确

### 问题3：参数格式错误
- 检查JSON格式是否正确
- 检查是否有语法错误

### 问题4：导出项目错误
- 检查通信模块是否正确生成
- 检查组件代码是否正确生成

## 成功标准

所有测试用例通过，功能完整可用，可以正常与易语言程序通信。
