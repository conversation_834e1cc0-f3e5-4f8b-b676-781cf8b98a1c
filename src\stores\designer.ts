import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'

// 组件类型定义
export interface ComponentData {
  id: string              // 组件唯一ID
  type: string            // 组件类型 (button, input, text等)
  name: string            // 组件名称
  x: number               // X坐标位置
  y: number               // Y坐标位置
  width: number           // 组件宽度
  height: number          // 组件高度
  props: Record<string, any>  // 组件属性
  style: Record<string, any>  // 组件样式
  events?: Record<string, any>  // 事件绑定配置
  children?: ComponentData[]  // 子组件列表（用于容器组件）
  parentId?: string       // 父组件ID
  hidden?: boolean        // 是否隐藏组件
}

// 画布配置
export interface CanvasConfig {
  width: number           // 画布宽度
  height: number          // 画布高度
  backgroundColor: string // 背景颜色
  gridSize: number        // 网格大小
  showGrid: boolean       // 是否显示网格
}

// 窗口数据接口
export interface WindowData {
  id: string              // 窗口唯一ID
  name: string            // 窗口名称
  route: string           // 窗口路由
  components: ComponentData[]  // 窗口内的组件列表
  selectedComponentId: string | null  // 当前选中的组件ID
  canvasConfig: CanvasConfig   // 窗口画布配置
  windowConfig: {              // 窗口配置
    width: number
    height: number
    backgroundColor: string
    border: string
    borderRadius: number
    boxShadow: string
  }
  windowType: 'normal' | 'borderless' // 窗口类型
  titleBarConfig: {            // 标题栏配置
    title: string              // 窗口标题（独立于窗口名称）
    showLogo: boolean          // 是否显示应用LOGO
    logoPath: string           // LOGO图片路径
    showDarkToggle: boolean    // 是否显示暗黑模式切换按钮
    showMinimize: boolean      // 是否显示最小化按钮
    showMaximize: boolean      // 是否显示最大化按钮
    showClose: boolean         // 是否显示关闭按钮
    isDarkMode: boolean        // 当前是否为暗黑模式
    titleColor: string         // 标题文字颜色
    backgroundColor: string    // 标题栏背景色
    height: number             // 标题栏高度
  }

}

// 服务组件类型定义
export interface ServiceComponentData {
  type: string; // 服务组件类型
  name: string; // 服务组件名称
  desc: string; // 服务组件描述
  // 其他可扩展属性
}

export const useDesignerStore = defineStore('designer', () => {
  // 多窗口数据管理
  const windows = ref<WindowData[]>([
    {
      id: 'window-main',
      name: '主窗口',
      route: 'home',
      components: [],
      selectedComponentId: null,
      canvasConfig: {
        width: 1200,
        height: 800,
        backgroundColor: '#ffffff',
        gridSize: 10,
        showGrid: true
      },
      windowConfig: {
        width: 800,
        height: 600,
        backgroundColor: '#fff',
        border: '1.5px solid #bfcbd9',
        borderRadius: 6,
        boxShadow: '0 8px 32px rgba(0,0,0,0.18)'
      },
      windowType: 'normal', // 默认有标题栏
      titleBarConfig: {     // 标题栏配置默认值
        title: '主窗口',
        showLogo: true,
        logoPath: '',
        showDarkToggle: true,
        showMinimize: true,
        showMaximize: true,
        showClose: true,
        isDarkMode: false,
        titleColor: '',
        backgroundColor: '',
        height: 36
      },

    }
  ])
  
  // 当前激活的窗口ID
  const activeWindowId = ref('window-main')
  
  // 是否处于预览模式
  const isPreviewMode = ref(false)
  
  // 是否为暗黑模式
  const isDarkMode = ref(false)
  
  // 全局视图控制状态
  const showGrid = ref(true)
  const showAlignLines = ref(true)
  const hasUnsavedChanges = ref(false)
  const currentProjectName = ref('未命名项目')
  const canUndo = ref(false)
  const canRedo = ref(false)
  
  // 全局服务组件列表（不会在画布上显示，但导出时包含）
  const serviceComponents = ref<ServiceComponentData[]>([])
  
  // 计算属性：当前激活窗口
  const activeWindow = computed(() => {
    return windows.value.find(w => w.id === activeWindowId.value) || windows.value[0]
  })
  
  // 计算属性：当前窗口的组件列表
  const components = computed(() => activeWindow.value.components)
  
  // 计算属性：当前窗口的选中组件ID
  const selectedComponentId = computed(() => activeWindow.value.selectedComponentId)
  
  // 计算属性：当前窗口的画布配置
  const canvasConfig = computed(() => activeWindow.value.canvasConfig)
  
  // 计算属性：当前窗口的窗口配置
  const windowConfig = computed(() => activeWindow.value.windowConfig)
  
  // 切换激活窗口
  const setActiveWindow = (windowId: string) => {
    const window = windows.value.find(w => w.id === windowId)
    if (window) {
      activeWindowId.value = windowId
    }
  }
  
  // 添加新窗口
  const addWindow = (windowData: Partial<WindowData>) => {
    const newWindow: WindowData = {
      id: windowData.id || generateSimpleId('window'),
      name: windowData.name || `窗口${windows.value.length + 1}`,
      route: windowData.route || `/window/${windowData.id || Date.now()}`,
      components: [],
      selectedComponentId: null,
      canvasConfig: {
        width: 1200,
        height: 800,
        backgroundColor: '#ffffff',
        gridSize: 10,
        showGrid: true
      },
      windowConfig: {
        width: 800,
        height: 600,
        backgroundColor: '#fff',
        border: '1.5px solid #bfcbd9',
        borderRadius: 6,
        boxShadow: '0 8px 32px rgba(0,0,0,0.18)'
      },
      windowType: windowData.windowType || 'normal',
      titleBarConfig: windowData.titleBarConfig || {
        title: windowData.name || `窗口${windows.value.length + 1}`,
        showLogo: true,
        logoPath: '',
        showDarkToggle: true,
        showMinimize: true,
        showMaximize: true,
        showClose: true,
        isDarkMode: false,
        titleColor: '',
        backgroundColor: '',
        height: 36
      },

    }
    windows.value.push(newWindow)
    activeWindowId.value = newWindow.id
    hasUnsavedChanges.value = true
    return newWindow.id
  }
  
  // 删除窗口
  const removeWindow = (windowId: string) => {
    const index = windows.value.findIndex(w => w.id === windowId)
    if (index > -1) {
      windows.value.splice(index, 1)
      // 如果删除的是当前激活窗口，切换到前一个窗口
      if (activeWindowId.value === windowId) {
        const newActiveId = windows.value[Math.max(0, index - 1)]?.id || windows.value[0]?.id
        if (newActiveId) {
          activeWindowId.value = newActiveId
        }
      }
      hasUnsavedChanges.value = true
    }
  }
  
  // 更新窗口配置
  const updateWindowConfig = (windowId: string, config: Partial<WindowData['windowConfig']>) => {
    const window = windows.value.find(w => w.id === windowId)
    if (window) {
      Object.assign(window.windowConfig, config)
      hasUnsavedChanges.value = true
    }
  }

  // 更新窗口基本信息
  const updateWindowInfo = (windowId: string, info: Partial<Pick<WindowData, 'name' | 'route' | 'windowType'>>) => {
    const window = windows.value.find(w => w.id === windowId)
    if (window) {
      Object.assign(window, info)
      hasUnsavedChanges.value = true
    }
  }

  // 更新标题栏配置
  const updateTitleBarConfig = (windowId: string, config: Partial<WindowData['titleBarConfig']>) => {
    const window = windows.value.find(w => w.id === windowId)
    if (window) {
      Object.assign(window.titleBarConfig, config)
      hasUnsavedChanges.value = true
    }
  }


  
  // 生成简化的组件ID
  const generateSimpleId = (type: string) => {
    const randomStr = Math.random().toString(36).substr(2, 8)
    return `${type}-${randomStr}`
  }

  // 判断组件是否支持子组件
  const isContainerComponent = (type: string): boolean => {
    const containerTypes = ['card', 'tabs', 'carousel', 'dialog', 'drawer', 'collapse']
    return containerTypes.includes(type)
  }

  // 添加组件到当前窗口
  const addComponent = (component: Omit<ComponentData, 'id'>, parentId?: string) => {
    const newComponent: ComponentData = {
      ...component,
      id: generateSimpleId(component.type),
      parentId,
      events: component.events || {} // 初始化事件绑定配置
    }

    if (parentId) {
      // 添加到父组件的children中
      const parentComponent = findComponentById(parentId)
      if (parentComponent) {
        if (!parentComponent.children) {
          parentComponent.children = []
        }
        parentComponent.children.push(newComponent)
      }
    } else {
      // 添加到窗口根级组件中
      activeWindow.value.components.push(newComponent)
    }

    activeWindow.value.selectedComponentId = newComponent.id
    hasUnsavedChanges.value = true
    return newComponent.id
  }
  
  // 递归查找组件
  const findComponentById = (id: string, components?: ComponentData[]): ComponentData | null => {
    const searchComponents = components || activeWindow.value.components
    for (const component of searchComponents) {
      if (component.id === id) {
        return component
      }
      if (component.children) {
        const found = findComponentById(id, component.children)
        if (found) return found
      }
    }
    return null
  }

  // 递归获取所有组件（包括嵌套的）
  const getAllComponents = (components?: ComponentData[]): ComponentData[] => {
    const searchComponents = components || activeWindow.value.components
    const result: ComponentData[] = []
    for (const component of searchComponents) {
      result.push(component)
      if (component.children) {
        result.push(...getAllComponents(component.children))
      }
    }
    return result
  }

  // 删除组件（支持嵌套）
  const removeComponent = (id: string) => {
    const removeFromArray = (components: ComponentData[]): boolean => {
      const index = components.findIndex(comp => comp.id === id)
      if (index > -1) {
        components.splice(index, 1)
        return true
      }
      // 递归查找子组件
      for (const component of components) {
        if (component.children && removeFromArray(component.children)) {
          return true
        }
      }
      return false
    }

    if (removeFromArray(activeWindow.value.components)) {
      if (activeWindow.value.selectedComponentId === id) {
        activeWindow.value.selectedComponentId = null
      }
      hasUnsavedChanges.value = true
    }
  }
  
  // 更新组件属性
  const updateComponent = (id: string, updates: Partial<ComponentData>) => {
    const component = findComponentById(id)
    if (component) {
      Object.assign(component, updates)
      hasUnsavedChanges.value = true
    }
  }
  
  // 获取选中的组件
  const getSelectedComponent = () => {
    if (activeWindow.value.selectedComponentId === '__window__') {
      // 返回窗口对象，type为'window'，要带上windowType
      return {
        type: 'window',
        id: activeWindow.value.id,
        name: activeWindow.value.name,
        windowType: activeWindow.value.windowType, // 必须加上
        ...activeWindow.value.windowConfig
      }
    }
    return findComponentById(activeWindow.value.selectedComponentId || '')
  }
  
  // 清空当前窗口画布
  const clearCanvas = () => {
    activeWindow.value.components = []
    activeWindow.value.selectedComponentId = null
  }
  
  // 设置选中组件
  const setSelectedComponent = (id: string | null) => {
    activeWindow.value.selectedComponentId = id
  }
  
  // 切换预览模式
  const togglePreviewMode = () => {
    isPreviewMode.value = !isPreviewMode.value
    if (isPreviewMode.value) {
      activeWindow.value.selectedComponentId = null
    }
  }
  
  // 切换暗黑模式
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
  }
  
  // 视图控制方法
  const toggleGrid = () => {
    showGrid.value = !showGrid.value
  }
  
  const toggleAlignLines = () => {
    showAlignLines.value = !showAlignLines.value
  }
  
  const setHasUnsavedChanges = (value: boolean) => {
    hasUnsavedChanges.value = value
  }
  
  const setCurrentProjectName = (name: string) => {
    currentProjectName.value = name
  }
  
  const setCanUndo = (value: boolean) => {
    canUndo.value = value
  }
  
  const setCanRedo = (value: boolean) => {
    canRedo.value = value
  }
  
  // 获取指定窗口的组件列表
  const getWindowComponents = (windowId: string) => {
    const window = windows.value.find(w => w.id === windowId)
    return window ? window.components : []
  }
  
  // 设置指定窗口的组件列表
  const setWindowComponents = (windowId: string, components: ComponentData[]) => {
    const window = windows.value.find(w => w.id === windowId)
    if (window) {
      window.components = components
    }
  }
  
  /**
   * 添加服务组件
   * @param svc 服务组件对象
   */
  const addServiceComponent = (svc: ServiceComponentData) => {
    if (!serviceComponents.value.some(item => item.type === svc.type)) {
      serviceComponents.value.push({ ...svc })
    }
  }

  /**
   * 删除服务组件
   * @param type 服务组件类型
   */
  const removeServiceComponent = (type: string) => {
    const idx = serviceComponents.value.findIndex(item => item.type === type)
    if (idx > -1) {
      serviceComponents.value.splice(idx, 1)
    }
  }

  /**
   * 获取导出数据（包含服务组件）
   */
  const getExportData = () => {
    return {
      windows: windows.value,
      serviceComponents: serviceComponents.value,
      projectName: currentProjectName.value
      // 可扩展其他导出内容
    }
  }
  
  // 新增：更新窗口类型，确保响应式
  const updateWindowType = (windowId: string, type: 'normal' | 'borderless') => {
    const window = windows.value.find(w => w.id === windowId)
    if (window) {
      // 直接修改对象属性，保持响应性
      window.windowType = type
      hasUnsavedChanges.value = true
      console.log('updateWindowType:', windowId, type, 'current windowType:', window.windowType)
    }
  }
  
  return {
    // 状态
    windows,
    activeWindowId,
    activeWindow,
    components,
    selectedComponentId,
    canvasConfig,
    windowConfig,
    isPreviewMode,
    isDarkMode,
    showGrid,
    showAlignLines,
    hasUnsavedChanges,
    currentProjectName,
    canUndo,
    canRedo,
    serviceComponents,
    
    // 方法
    setActiveWindow,
    addWindow,
    removeWindow,
    updateWindowConfig,
    updateWindowInfo,
    updateTitleBarConfig,

    addComponent,
    removeComponent,
    updateComponent,
    getSelectedComponent,
    findComponentById,
    getAllComponents,
    clearCanvas,
    setSelectedComponent,
    togglePreviewMode,
    toggleDarkMode,
    toggleGrid,
    toggleAlignLines,
    setHasUnsavedChanges,
    setCurrentProjectName,
    setCanUndo,
    setCanRedo,
    getWindowComponents,
    setWindowComponents,
    addServiceComponent,
    removeServiceComponent,
    getExportData,
    updateWindowType
  }
}) 