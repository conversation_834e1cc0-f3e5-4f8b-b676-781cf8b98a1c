# 完整事件绑定功能说明

## 功能概述

现在已经在属性面板中添加了完整的"事件绑定"栏，用于配置组件与易语言的通信。支持多种触发方式、参数传递和大数据处理。

## 使用方法

### 1. 在设计器中配置事件绑定

1. 在设计器中添加一个组件（如按钮、输入框、表格等）
2. 选中该组件
3. 在右侧属性面板中找到"事件绑定"栏
4. 配置以下字段：

#### 事件绑定配置字段

1. **是否启用通信** (勾选框)
   - 说明：是否开启与易语言交互（防止无意中触发）
   - 默认：关闭

2. **通信命令名** (文本型)
   - 说明：点击该组件时发送给易语言的命令字符串
   - 示例：`user_click_login`

3. **通信参数** (键值对JSON)
   - 说明：可选附加参数，自动作为参数发送
   - 示例：`{"username":"admin","type":"login"}`

4. **通信触发方式** (下拉选择)
   - 点击时触发 (`click`)
   - 值变化时触发 (`change`)
   - 表单提交时触发 (`submit`)
   - 双击时触发 (`dblclick`)
   - 获得焦点时触发 (`focus`)
   - 失去焦点时触发 (`blur`)

### 2. 支持的组件类型

所有组件都支持事件绑定，根据组件类型有不同的默认触发方式：

- **按钮组件**: 默认点击触发
- **输入框组件**: 默认值变化触发
- **选择框组件**: 默认值变化触发
- **表格组件**: 默认值变化触发（支持行选择、点击等）
- **文本组件**: 默认点击触发
- **图片组件**: 默认点击触发
- **其他组件**: 默认点击触发

### 3. 事件数据结构

当事件触发时，会传递以下数据结构：

```javascript
{
  cmd: "user_click_login",        // 配置的通信命令名
  payload: {                      // 配置的参数 + 事件数据
    username: "admin",            // 用户配置的参数
    value: "输入的内容"           // 组件的当前值（如适用）
  },
  componentId: "button-abc123",   // 组件唯一ID
  timestamp: 1640995200000        // 事件触发时间戳
}
```

### 4. 与易语言通信机制

导出的项目包含完整的通信模块 (`communication.js`)，支持多种通信方式：

#### 4.1 WebView2 通信（推荐）
```javascript
if (window.chrome && window.chrome.webview) {
  window.chrome.webview.postMessage(message);
}
```

#### 4.2 传统 WebBrowser 控件通信
```javascript
if (window.external && window.external.notify) {
  window.external.notify(JSON.stringify(message));
}
```

#### 4.3 表格数据专用通信
```javascript
// 自动处理表格数据的特殊格式
sendTableData(cmd, tableData, componentId)
```

#### 4.4 大数据分批发送
```javascript
// 自动分批发送大量数据，避免通信阻塞
sendLargeData(cmd, data, componentId, chunkSize)
```

### 5. 表格组件特殊处理

表格组件支持多种数据通信方式：

- **行点击**: 发送单行数据
- **行选择**: 发送选中的多行数据
- **双击**: 发送单行数据
- **数据变化**: 发送完整表格数据

表格数据会自动包装为特殊格式：
```javascript
{
  type: 'table',
  data: [...],              // 实际数据
  rowCount: 10,            // 行数
  columnCount: 5           // 列数
}
```

### 6. 开发环境测试

在开发环境中，事件触发时会：
1. 在浏览器控制台输出详细事件信息
2. 显示Element Plus消息提示
3. 模拟易语言通信接口

## 技术实现详解

### 数据结构更新

在 `ComponentData` 接口中添加了完整的 `events` 属性：

```typescript
export interface ComponentData {
  // ... 其他属性
  events?: {
    enabled: boolean,         // 是否启用通信
    cmd: string,             // 通信命令名
    payload: string,         // 通信参数（JSON字符串）
    trigger: string          // 触发方式
  }
}
```

### 属性面板完整实现

```vue
<!-- 事件绑定 -->
<div class="property-section">
  <h4 class="section-title">事件绑定</h4>
  <el-form label-width="100px" size="small">
    <el-form-item label="是否启用通信">
      <el-switch v-model="events.enabled" />
    </el-form-item>

    <template v-if="events.enabled">
      <el-form-item label="通信命令名">
        <el-input v-model="events.cmd" placeholder="如：user_click_login" />
      </el-form-item>

      <el-form-item label="通信参数">
        <el-input v-model="events.payload" type="textarea"
                  placeholder='如：{"username":"admin"}' />
      </el-form-item>

      <el-form-item label="通信触发方式">
        <el-select v-model="events.trigger">
          <el-option label="点击时触发" value="click" />
          <el-option label="值变化时触发" value="change" />
          <!-- 更多选项... -->
        </el-select>
      </el-form-item>
    </template>
  </el-form>
</div>
```

### 导出功能核心实现

#### 通信模块生成
自动生成 `communication.js` 文件，包含：
- `sendCommandToApp()` - 通用命令发送
- `sendTableData()` - 表格数据专用
- `sendLargeData()` - 大数据分批发送

#### 组件事件处理生成
```javascript
// 智能事件处理器生成
function generateEventHandler(component, defaultTrigger) {
  if (!component.events?.enabled) return `@${defaultTrigger}="handleButtonClick"`

  const trigger = component.events.trigger || defaultTrigger
  const cmd = component.events.cmd
  const payload = component.events.payload || '{}'

  return `@${trigger}="sendCommandToApp('${cmd}', '${payload}', '${component.id}')"`
}
```

## 使用示例

### 示例1：登录按钮
1. 添加按钮组件，设置文字为"登录"
2. 启用事件绑定
3. 设置命令名：`user_login`
4. 设置参数：`{"action":"login","page":"main"}`
5. 选择触发方式：点击时触发

### 示例2：用户输入框
1. 添加输入框组件
2. 启用事件绑定
3. 设置命令名：`user_input_change`
4. 设置参数：`{"field":"username"}`
5. 选择触发方式：值变化时触发

### 示例3：数据表格
1. 添加表格组件，配置列信息
2. 启用事件绑定
3. 设置命令名：`table_row_selected`
4. 设置参数：`{"table":"user_list"}`
5. 选择触发方式：值变化时触发（行选择）

## 易语言接收示例

```易语言
' WebView2 消息接收
子程序 WebView_消息接收, , 公开,
参数 消息内容, 文本型

局部变量 json对象, 类_JSON
局部变量 命令, 文本型
局部变量 参数, 文本型
局部变量 组件ID, 文本型

json对象.解析 (消息内容)
命令 = json对象.取文本值 ("cmd")
参数 = json对象.取文本值 ("payload")
组件ID = json对象.取文本值 ("componentId")

判断开始 (命令)
    案例 "user_login"
        处理用户登录 (参数, 组件ID)
    案例 "user_input_change"
        处理输入变化 (参数, 组件ID)
    案例 "table_row_selected"
        处理表格选择 (参数, 组件ID)
判断结束
```

这样就实现了完整的前端UI与易语言程序的双向通信机制！
