# 事件绑定功能说明

## 功能概述

现在已经在属性面板中添加了"事件绑定"栏，用于配置组件与易语言的通信。

## 使用方法

### 1. 在设计器中配置事件绑定

1. 在设计器中添加一个组件（如按钮、输入框等）
2. 选中该组件
3. 在右侧属性面板中找到"事件绑定"栏
4. 在"通信命令名"字段中输入命令名称，例如：`user_click_login`

### 2. 支持的组件类型

目前支持事件绑定的组件：
- **按钮组件**: 点击事件 (`@click`)
- **输入框组件**: 内容变化事件 (`@change`)

### 3. 事件数据结构

当事件触发时，会传递以下数据结构：

```javascript
{
  cmd: "user_click_login",        // 配置的通信命令名
  componentId: "button-abc123",   // 组件唯一ID
  data: eventData                 // 事件相关数据（如输入框的值）
}
```

### 4. 与易语言通信

在导出的项目中，事件处理函数会尝试通过以下方式与易语言通信：

```javascript
// 如果在易语言WebBrowser控件中运行
if (window.external && window.external.notify) {
  window.external.notify(JSON.stringify({
    cmd: "user_click_login",
    componentId: "button-abc123", 
    data: eventData
  }))
}
```

### 5. 开发环境测试

在开发环境中，事件触发时会：
1. 在浏览器控制台输出事件信息
2. 显示Element Plus消息提示

## 技术实现

### 数据结构更新

在 `ComponentData` 接口中添加了 `events` 属性：

```typescript
export interface ComponentData {
  // ... 其他属性
  events?: Record<string, any>  // 事件绑定配置
}
```

### 属性面板更新

在 `PropertyPanel.vue` 中添加了事件绑定栏：

```vue
<!-- 事件绑定 -->
<div class="property-section">
  <h4 class="section-title">事件绑定</h4>
  <el-form label-width="80px" size="small">
    <el-form-item label="通信命令名">
      <el-input 
        :value="selectedComponent.events?.cmd || ''" 
        @input="updateEvent('cmd', $event)"
        placeholder="如：user_click_login"
        clearable
      />
      <div class="form-item-desc">用于与易语言通信的命令名称</div>
    </el-form-item>
  </el-form>
</div>
```

### 导出功能更新

在 `exportProject.ts` 中更新了组件代码生成，支持事件绑定：

```javascript
// 按钮组件
const buttonClickHandler = component.events?.cmd ? 
  `handleComponentEvent('${component.events.cmd}', '${component.id}')` : 
  'handleButtonClick'

// 输入框组件  
const inputChangeHandler = component.events?.cmd ? 
  `@change="handleComponentEvent('${component.events.cmd}', '${component.id}', $event)"` : 
  ''
```

## 使用示例

1. 添加一个按钮组件
2. 设置按钮文字为"登录"
3. 在事件绑定中设置通信命令名为"user_click_login"
4. 导出项目后，点击按钮会触发与易语言的通信

这样就可以实现前端UI与易语言程序的交互通信。
