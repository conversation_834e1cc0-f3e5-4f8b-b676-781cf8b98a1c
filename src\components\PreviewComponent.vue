<template>
  <div
    class="preview-component"
    :class="{ 'window-dark-mode': windowDarkMode }"
    :style="componentStyle"
  >
    <!-- 组件内容容器 -->
    <div class="component-inner" :class="{ 'image-container': componentData.type === 'image' }">
      <!-- 根据组件类型渲染不同的组件 -->
    <!-- 输入框 -->
    <el-input
      v-if="componentData.type === 'input'"
      v-model="inputValue"
      :type="componentData.props?.inputType || 'text'"
      :placeholder="componentData.props?.placeholder || '请输入内容'"
      :disabled="componentData.props?.disabled"
      :readonly="componentData.props?.readonly"
      :clearable="componentData.props?.clearable"
      :show-password="componentData.props?.showPassword"
      :show-word-limit="componentData.props?.showWordLimit"
      :maxlength="componentData.props?.maxlength"
      :minlength="componentData.props?.minlength"
      :size="componentData.props?.size || 'default'"
      :prefix-icon="getIconComponent(componentData.props?.prefixIcon)"
      :suffix-icon="getIconComponent(componentData.props?.suffixIcon)"
      :autocomplete="componentData.props?.autocomplete || 'off'"
      :name="componentData.props?.name"
      :form="componentData.props?.form"
      :label="componentData.props?.label"
      :tabindex="componentData.props?.tabindex"
      :validate-event="componentData.props?.validateEvent !== false"
      style="width: 100%; height: 100%;"
      @change="handleInputChange"
    />

    <!-- 按钮 -->
    <el-button
      v-else-if="componentData.type === 'button'"
      style="width:100%;height:100%;"
      :type="componentData.props?.type || 'primary'"
      :size="componentData.props?.size || 'default'"
      :plain="!!componentData.props?.plain"
      :round="!!componentData.props?.round"
      :circle="!!componentData.props?.circle"
      :icon="getIconComponent(componentData.props?.icon)"
      :disabled="!!componentData.props?.disabled"
      :loading="!!componentData.props?.loading"
      :autofocus="!!componentData.props?.autofocus"
      :native-type="componentData.props?.nativeType || 'button'"
      @click="handleComponentClick"
    >
      {{ componentData.props?.text || '按钮' }}
    </el-button>

    <!-- 文本 -->
    <el-text
      v-else-if="componentData.type === 'text'"
      :type="componentData.props?.type || 'primary'"
      :size="componentData.props?.size || 'default'"
      :tag="componentData.props?.tag || 'span'"
      :truncated="componentData.props?.truncated"
      :line-clamp="componentData.props?.lineClamp"
      style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;"
    >
      {{ componentData.props?.content || '文本内容' }}
    </el-text>

    <!-- 图片 -->
    <el-image
      v-else-if="componentData.type === 'image'"
      :src="componentData.props?.src || 'https://via.placeholder.com/150'"
      :alt="componentData.props?.alt || '图片'"
      :fit="componentData.props?.fit || 'cover'"
      :loading="componentData.props?.loading || 'eager'"
      :lazy="componentData.props?.lazy"
      :scroll-container="componentData.props?.scrollContainer"
      :preview-src-list="componentData.props?.previewSrcList"
      :z-index="componentData.props?.zIndex"
      :initial-index="componentData.props?.initialIndex || 0"
      :close-on-press-escape="componentData.props?.closeOnPressEscape !== false"
      :preview-teleported="componentData.props?.previewTeleported !== false"
      :style="{
        width: '100%',
        height: '100%',
        display: 'block',
        minHeight: '0',
        maxHeight: 'none'
      }"
    />

    <!-- 选择器 -->
    <el-select
      v-else-if="componentData.type === 'select'"
      v-model="selectValue"
      :placeholder="componentData.props?.placeholder || '请选择'"
      :disabled="componentData.props?.disabled"
      :clearable="componentData.props?.clearable"
      :multiple="componentData.props?.multiple"
      :size="componentData.props?.size || 'default'"
      style="width: 100%; height: 100%;"
    >
      <el-option
        v-for="option in componentData.props?.options || []"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      />
    </el-select>

    <!-- 多行文本框 -->
    <el-input
      v-else-if="componentData.type === 'textarea'"
      v-model="textareaValue"
      type="textarea"
      :placeholder="componentData.props?.placeholder || '请输入内容'"
      :disabled="componentData.props?.disabled"
      :readonly="componentData.props?.readonly"
      :rows="componentData.props?.rows || 3"
      :maxlength="componentData.props?.maxlength"
      :show-word-limit="componentData.props?.showWordLimit"
      :resize="componentData.props?.resize || 'vertical'"
      style="width: 100%; height: 100%;"
    />

    <!-- 复选框 -->
    <el-checkbox
      v-else-if="componentData.type === 'checkbox'"
      v-model="checkboxValue"
      :disabled="componentData.props?.disabled"
      :indeterminate="componentData.props?.indeterminate"
      :size="componentData.props?.size || 'default'"
    >
      {{ componentData.props?.label || '复选框' }}
    </el-checkbox>

    <!-- 单选框 -->
    <el-radio
      v-else-if="componentData.type === 'radio'"
      v-model="radioValue"
      :label="componentData.props?.value || 'option1'"
      :disabled="componentData.props?.disabled"
      :size="componentData.props?.size || 'default'"
    >
      {{ componentData.props?.label || '单选框' }}
    </el-radio>

    <!-- 开关 -->
    <el-switch
      v-else-if="componentData.type === 'switch'"
      v-model="switchValue"
      :disabled="componentData.props?.disabled"
      :size="componentData.props?.size || 'default'"
      :active-text="componentData.props?.activeText"
      :inactive-text="componentData.props?.inactiveText"
      :active-color="componentData.props?.activeColor"
      :inactive-color="componentData.props?.inactiveColor"
    />

    <!-- 滑块 -->
    <el-slider
      v-else-if="componentData.type === 'slider'"
      v-model="sliderValue"
      :min="componentData.props?.min || 0"
      :max="componentData.props?.max || 100"
      :step="componentData.props?.step || 1"
      :disabled="componentData.props?.disabled"
      :show-input="componentData.props?.showInput"
      :show-stops="componentData.props?.showStops"
      :range="componentData.props?.range"
      style="width: 100%"
    />

    <!-- 日期选择器 -->
    <el-date-picker
      v-else-if="componentData.type === 'date-picker'"
      v-model="dateValue"
      :type="componentData.props?.type || 'date'"
      :placeholder="componentData.props?.placeholder || '选择日期'"
      :disabled="componentData.props?.disabled"
      :clearable="componentData.props?.clearable"
      :size="componentData.props?.size || 'default'"
      style="width: 100%; height: 100%;"
    />

    <!-- 时间选择器 -->
    <el-time-picker
      v-else-if="componentData.type === 'time-picker'"
      v-model="timeValue"
      :placeholder="componentData.props?.placeholder || '选择时间'"
      :disabled="componentData.props?.disabled"
      :clearable="componentData.props?.clearable"
      :size="componentData.props?.size || 'default'"
      style="width: 100%; height: 100%;"
    />

    <!-- 评分 -->
    <el-rate
      v-else-if="componentData.type === 'rate'"
      v-model="rateValue"
      :max="componentData.props?.max || 5"
      :disabled="componentData.props?.disabled"
      :allow-half="componentData.props?.allowHalf"
      :show-text="componentData.props?.showText"
      :show-score="componentData.props?.showScore"
    />

    <!-- 颜色选择器 -->
    <el-color-picker
      v-else-if="componentData.type === 'color-picker'"
      v-model="colorValue"
      :disabled="componentData.props?.disabled"
      :size="componentData.props?.size || 'default'"
      :show-alpha="componentData.props?.showAlpha"
      :color-format="componentData.props?.colorFormat"
    />

    <!-- 卡片组件 -->
    <el-card
      v-else-if="componentData.type === 'card'"
      :header="componentData.props?.header"
      :shadow="componentData.props?.shadow || 'always'"
      :body-style="componentData.props?.bodyStyle || {}"
      style="width: 100%; height: 100%;"
    >
      <!-- 渲染子组件 -->
      <div class="card-container" style="position: relative; width: 100%; height: 100%;">
        <PreviewComponent
          v-for="child in componentData.children || []"
          :key="child.id"
          :component-data="child"
          :window-dark-mode="windowDarkMode"
        />
        <!-- 默认内容 -->
        <div v-if="!componentData.children || componentData.children.length === 0" style="padding: 20px; text-align: center; color: #999;">
          {{ componentData.props?.content || '卡片内容' }}
        </div>
      </div>
    </el-card>

    <!-- 走马灯组件 -->
    <el-carousel
      v-else-if="componentData.type === 'carousel'"
      :height="componentData.props?.height || '150px'"
      :trigger="componentData.props?.trigger || 'hover'"
      :autoplay="componentData.props?.autoplay !== false"
      :interval="componentData.props?.interval || 4000"
      :indicator-position="componentData.props?.indicator ? 'outside' : 'none'"
      :arrow="componentData.props?.arrow || 'hover'"
      :type="componentData.props?.type || ''"
      :loop="componentData.props?.loop !== false"
      :direction="componentData.props?.direction || 'horizontal'"
      style="width: 100%; height: 100%;"
    >
      <el-carousel-item
        v-for="(item, index) in componentData.props?.items || []"
        :key="index"
        :style="{ backgroundColor: item.color || '#99a9bf' }"
      >
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 14px;">
          {{ item.content || `轮播项 ${index + 1}` }}
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 标签组件 -->
    <el-tag
      v-else-if="componentData.type === 'tag'"
      :type="componentData.props?.type || ''"
      :closable="componentData.props?.closable"
      :disable-transitions="componentData.props?.disableTransitions"
      :hit="componentData.props?.hit"
      :color="componentData.props?.color"
      :size="componentData.props?.size || 'default'"
      :effect="componentData.props?.effect || 'light'"
      :round="componentData.props?.round"
    >
      {{ componentData.props?.text || '标签' }}
    </el-tag>

    <!-- 标签页组件 -->
    <el-tabs
      v-else-if="componentData.type === 'tabs'"
      :model-value="componentData.props?.activeTab || 'first'"
      :type="componentData.props?.type || ''"
      :closable="componentData.props?.closable"
      :addable="componentData.props?.addable"
      :editable="componentData.props?.editable"
      :tab-position="componentData.props?.tabPosition || 'top'"
      :stretch="componentData.props?.stretch"
      style="width: 100%; height: 100%;"
    >
      <el-tab-pane
        v-for="tab in componentData.props?.tabs || []"
        :key="tab.name"
        :label="tab.label"
        :name="tab.name"
      >
        <!-- 渲染标签页内的子组件 -->
        <div class="tab-container" style="position: relative; width: 100%; height: 100%; min-height: 200px;">
          <PreviewComponent
            v-for="child in getTabChildren(tab.name)"
            :key="child.id"
            :component-data="child"
            :window-dark-mode="windowDarkMode"
          />
          <!-- 默认内容 -->
          <div v-if="!getTabChildren(tab.name) || getTabChildren(tab.name).length === 0" style="padding: 20px; text-align: center; color: #999;">
            {{ tab.content || '标签页内容' }}
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 表格组件 -->
    <el-table
      v-else-if="componentData.type === 'table'"
      :data="componentData.props?.data || []"
      :stripe="componentData.props?.stripe"
      :border="componentData.props?.border"
      :size="componentData.props?.size || 'default'"
      :fit="componentData.props?.fit !== false"
      :show-header="componentData.props?.showHeader !== false"
      :highlight-current-row="componentData.props?.highlightCurrentRow"
      :empty-text="componentData.props?.emptyText || '暂无数据'"
      style="width: 100%; height: 100%;"
    >
      <el-table-column
        v-for="column in componentData.props?.columns || []"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :sortable="column.sortable"
        :resizable="column.resizable"
        :formatter="column.formatter"
        :show-overflow-tooltip="column.showOverflowTooltip"
        :align="column.align"
        :header-align="column.headerAlign"
        :class-name="column.className"
        :label-class-name="column.labelClassName"
      />
    </el-table>

      <!-- 默认显示组件类型 -->
      <div v-else class="unknown-component">
        <el-tag type="info">{{ componentData.type || '未知组件' }}</el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue'
import { ElMessage } from 'element-plus'
import type { ComponentData } from '@/stores/designer'
import {
  IconHome, IconUser, IconSettings, IconSearch, IconPlus, IconMinus, IconX, IconCheck,
  IconArrowLeft, IconArrowRight, IconArrowUp, IconArrowDown, IconEdit, IconTrash,
  IconEye, IconEyeOff, IconHeart, IconStar, IconBell, IconMail, IconPhone, IconLock,
  IconDownload, IconUpload, IconShare, IconCopy, IconRefresh, IconZoomIn, IconZoomOut,
  IconPlayerStop, IconPlayerSkipBack, IconPlayerSkipForward,
  IconVolume, IconVolumeOff, IconWifi, IconBluetooth, IconBattery, IconCpu,
  IconCamera, IconVideo, IconPhoto, IconFile, IconFolder, IconCode, IconTerminal,
  IconBrandGithub, IconBrandGoogle, IconBrandFacebook, IconBrandTwitter,
  IconCalendar, IconClock, IconMap, IconMapPin, IconSun, IconMoon, IconCloud,
  IconBookmark, IconTag, IconFilter, IconSortAscending, IconList, IconGrid3x3,
  IconChartBar, IconChartPie, IconChartLine, IconDatabase, IconServer, IconShield,
  IconKey, IconQrcode, IconPalette, IconBrush, IconPencil, IconRocket, IconCar
} from '@tabler/icons-vue'

// 定义props
const props = defineProps<{
  componentData: ComponentData
  windowDarkMode: boolean
}>()

// 注入窗口暗黑模式状态
const windowDarkMode = inject('windowDarkMode', ref(false))

// 预定义的图标映射
const iconMap = {
  IconHome, IconUser, IconSettings, IconSearch, IconPlus, IconMinus, IconX, IconCheck,
  IconArrowLeft, IconArrowRight, IconArrowUp, IconArrowDown, IconEdit, IconTrash,
  IconEye, IconEyeOff, IconHeart, IconStar, IconBell, IconMail, IconPhone, IconLock,
  IconDownload, IconUpload, IconShare, IconCopy, IconRefresh, IconZoomIn, IconZoomOut,
  IconPlayerStop, IconPlayerSkipBack, IconPlayerSkipForward,
  IconVolume, IconVolumeOff, IconWifi, IconBluetooth, IconBattery, IconCpu,
  IconCamera, IconVideo, IconPhoto, IconFile, IconFolder, IconCode, IconTerminal,
  IconBrandGithub, IconBrandGoogle, IconBrandFacebook, IconBrandTwitter,
  IconCalendar, IconClock, IconMap, IconMapPin, IconSun, IconMoon, IconCloud,
  IconBookmark, IconTag, IconFilter, IconSortAscending, IconList, IconGrid3x3,
  IconChartBar, IconChartPie, IconChartLine, IconDatabase, IconServer, IconShield,
  IconKey, IconQrcode, IconPalette, IconBrush, IconPencil, IconRocket, IconCar
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  if (!iconName) return null
  return (iconMap as any)[iconName] || null
}

// 组件数据状态
const inputValue = ref(props.componentData.props?.value || '')
const selectValue = ref(props.componentData.props?.value || '')
const textareaValue = ref(props.componentData.props?.value || '')
const checkboxValue = ref(props.componentData.props?.checked || false)
const radioValue = ref(props.componentData.props?.value || '')
const switchValue = ref(props.componentData.props?.value || false)
const sliderValue = ref(props.componentData.props?.value || 0)
const dateValue = ref(props.componentData.props?.value || null)
const timeValue = ref(props.componentData.props?.value || null)
const rateValue = ref(props.componentData.props?.value || 0)
const colorValue = ref(props.componentData.props?.value || '#409EFF')

// 计算样式
const componentStyle = computed(() => {
  // 先合并 style，再强制覆盖 width/height，防止 style 里的 width/height 覆盖受控属性
  const style = { ...props.componentData.style };
  style.width = props.componentData.width + 'px';
  style.height = props.componentData.height + 'px';
  return {
    position: 'absolute' as const,
    left: props.componentData.x + 'px',
    top: props.componentData.y + 'px',
    ...style
  }
})

// 获取标签页的子组件
const getTabChildren = (tabName: string) => {
  if (!props.componentData.children) return []
  return props.componentData.children.filter(child => {
    // 如果组件有tabName属性，则匹配对应的标签页
    if (child.props?.tabName) {
      return child.props.tabName === tabName
    }
    // 如果组件没有tabName属性，默认显示在第一个标签页
    const firstTab = props.componentData.props?.tabs?.[0]
    return tabName === (firstTab?.name || 'first')
  })
}

// 事件处理
const handleButtonClick = () => {
  ElMessage.success(`按钮 "${props.componentData.props?.text || '按钮'}" 被点击`)
}

// 通用发送命令接口（预览环境）
const sendCommandToApp = (cmd: string, payload: any = {}, componentId: string) => {
  const message = {
    cmd,
    payload: typeof payload === 'string' ? JSON.parse(payload || '{}') : payload,
    componentId,
    timestamp: Date.now()
  }

  console.log('预览环境 - 发送命令到易语言:', message)
  ElMessage.info(`事件触发: ${cmd}`)
}

// 处理组件事件绑定
const handleComponentClick = () => {
  if (props.componentData.events?.enabled && props.componentData.events?.cmd) {
    const cmd = props.componentData.events.cmd
    const payload = props.componentData.events.payload || '{}'
    const componentId = props.componentData.id

    sendCommandToApp(cmd, payload, componentId)
  } else {
    handleButtonClick()
  }
}

// 处理输入框变化事件
const handleInputChange = (value: any) => {
  if (props.componentData.events?.enabled && props.componentData.events?.cmd) {
    const cmd = props.componentData.events.cmd
    const payload = JSON.parse(props.componentData.events.payload || '{}')
    const componentId = props.componentData.id

    sendCommandToApp(cmd, { ...payload, value }, componentId)
  }
}
</script>

<style scoped>
.preview-component {
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 组件内容容器 - 与设计器保持一致 */
.component-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统一组件拉伸样式 - 与设计器保持一致 */
.component-inner :deep(.el-input),
.component-inner :deep(.el-input__wrapper),
.component-inner :deep(.el-input__inner) {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}

/* 图片组件样式 */
.component-inner :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  display: block !important;
  flex: none !important;
  position: relative !important;
}

.component-inner :deep(.el-image__inner) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  object-fit: inherit !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* 图片组件容器特殊处理 - 避免flex布局影响 */
.component-inner.image-container {
  display: block !important;
  position: relative !important;
}

/* 强制覆盖Element Plus图片组件的所有可能样式 */
.preview-component :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  line-height: normal !important;
}

.preview-component :deep(.el-image__inner) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
}

/* 按钮组件样式 */
.component-inner :deep(.el-button) {
  width: 100% !important;
  height: 100% !important;
}

/* 选择器组件样式 */
.component-inner :deep(.el-select) {
  width: 100% !important;
}

.component-inner :deep(.el-select .el-input__wrapper) {
  width: 100% !important;
  height: 100% !important;
}

/* 文本域组件样式 */
.component-inner :deep(.el-textarea) {
  width: 100% !important;
  height: 100% !important;
}

.component-inner :deep(.el-textarea__inner) {
  width: 100% !important;
  height: 100% !important;
  resize: none !important;
}

/* 日期时间选择器样式 */
.component-inner :deep(.el-date-editor),
.component-inner :deep(.el-time-picker) {
  width: 100% !important;
  height: 100% !important;
}

.component-inner :deep(.el-date-editor .el-input__wrapper),
.component-inner :deep(.el-time-picker .el-input__wrapper) {
  width: 100% !important;
  height: 100% !important;
}

/* 预览模式下组件的暗黑模式样式 */
.preview-component.window-dark-mode :deep(.el-input) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
  --el-input-placeholder-color: #8c8c8c !important;
}

.preview-component.window-dark-mode :deep(.el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode :deep(.el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

.preview-component.window-dark-mode :deep(.el-button) {
  --el-button-bg-color: #404040 !important;
  --el-button-border-color: #606060 !important;
  --el-button-text-color: #ffffff !important;
}

.preview-component.window-dark-mode :deep(.el-button--primary) {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.preview-component.window-dark-mode :deep(.el-select) {
  --el-select-input-color: #ffffff !important;
}

.preview-component.window-dark-mode :deep(.el-select .el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.preview-component.window-dark-mode :deep(.el-textarea) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.preview-component.window-dark-mode :deep(.el-textarea__inner) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode span {
  color: #ffffff !important;
}

.unknown-component {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  background: #f5f7fa;
}

.preview-component.window-dark-mode .unknown-component {
  border-color: #404040;
  background: #2c2c2c;
}
</style>
