import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import type { WindowData, ComponentData } from '@/stores/designer'

// 导出项目为完整的Vue3+ElementPlus项目
export async function exportProject(windows: WindowData[], projectName: string = 'easy-window-project') {
  const zip = new JSZip()

  // 创建项目基础结构
  await createProjectStructure(zip, windows, projectName)

  // 生成ZIP文件并下载
  const content = await zip.generateAsync({ type: 'blob' })
  saveAs(content, `${projectName}.zip`)
}

// 创建项目结构
async function createProjectStructure(zip: JSZip, windows: WindowData[], projectName: string) {
  // 1. 创建package.json
  zip.file('package.json', generatePackageJson(projectName))

  // 2. 创建vite.config.ts
  zip.file('vite.config.ts', generateViteConfig())

  // 3. 创建tsconfig.json
  zip.file('tsconfig.json', generateTsConfig())

  // 3.1 创建tsconfig.node.json
  zip.file('tsconfig.node.json', generateTsNodeConfig())

  // 4. 创建index.html
  zip.file('index.html', generateIndexHtml(projectName))

  // 5. 创建src目录结构
  const srcFolder = zip.folder('src')!
  
  // 创建main.ts
  srcFolder.file('main.ts', generateMainTs())

  // 创建App.vue
  srcFolder.file('App.vue', generateAppVue())

  // 创建路由配置
  const routerFolder = srcFolder.folder('router')!
  routerFolder.file('index.ts', generateRouterConfig(windows))

  // 创建全局状态管理
  const storesFolder = srcFolder.folder('stores')!
  storesFolder.file('global.ts', generateGlobalStore())

  // 创建窗口组件
  const viewsFolder = srcFolder.folder('views')!
  for (const window of windows) {
    viewsFolder.file(`${window.name}.vue`, generateWindowComponent(window))
  }

  // 创建组件文件夹
  const componentsFolder = srcFolder.folder('components')!
  componentsFolder.file('WindowTitleBar.vue', generateWindowTitleBarComponent())

  // 创建样式文件
  srcFolder.file('style.css', generateGlobalStyles())

  // 6. 创建public目录
  const publicFolder = zip.folder('public')!
  
  // 复制LOGO文件（如果有的话）
  for (const window of windows) {
    if (window.titleBarConfig?.logoPath && window.titleBarConfig.logoPath.startsWith('/logos/')) {
      // 这里可以添加LOGO文件的复制逻辑
    }
  }

  // 7. 创建README.md
  zip.file('README.md', generateReadme(projectName))

  // 8. 创建.gitignore
  zip.file('.gitignore', generateGitignore())
}

// 生成package.json
function generatePackageJson(projectName: string): string {
  return JSON.stringify({
    name: projectName.toLowerCase().replace(/\s+/g, '-'),
    private: true,
    version: '0.0.0',
    type: 'module',
    scripts: {
      dev: 'vite',
      build: 'vue-tsc && vite build',
      preview: 'vite preview'
    },
    dependencies: {
      vue: '^3.4.0',
      'vue-router': '^4.2.5',
      'element-plus': '^2.4.4',
      '@element-plus/icons-vue': '^2.3.1',
      'pinia': '^2.1.7'
    },
    devDependencies: {
      '@types/node': '^20.10.0',
      '@vitejs/plugin-vue': '^4.5.2',
      typescript: '^5.2.2',
      'vue-tsc': '^1.8.25',
      vite: '^5.0.8',
      terser: '^5.24.0'
    }
  }, null, 2)
}

// 生成vite.config.ts
function generateViteConfig(): string {
  return `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          elementPlus: ['element-plus', '@element-plus/icons-vue']
        }
      }
    }
  }
})
`
}

// 生成tsconfig.json
function generateTsConfig(): string {
  return JSON.stringify({
    compilerOptions: {
      target: 'ES2020',
      useDefineForClassFields: true,
      lib: ['ES2020', 'DOM', 'DOM.Iterable'],
      module: 'ESNext',
      skipLibCheck: true,
      moduleResolution: 'bundler',
      allowImportingTsExtensions: true,
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'preserve',
      strict: true,
      noUnusedLocals: false,
      noUnusedParameters: false,
      noFallthroughCasesInSwitch: true,
      baseUrl: '.',
      paths: {
        '@/*': ['src/*']
      }
    },
    include: ['src/**/*.ts', 'src/**/*.d.ts', 'src/**/*.tsx', 'src/**/*.vue'],
    references: [{ path: './tsconfig.node.json' }]
  }, null, 2)
}

// 生成tsconfig.node.json
function generateTsNodeConfig(): string {
  return JSON.stringify({
    compilerOptions: {
      composite: true,
      skipLibCheck: true,
      module: 'ESNext',
      moduleResolution: 'bundler',
      allowSyntheticDefaultImports: true
    },
    include: ['vite.config.ts']
  }, null, 2)
}

// 生成index.html
function generateIndexHtml(projectName: string): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${projectName}</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
`
}

// 生成main.ts
function generateMainTs(): string {
  return `import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import App from './App.vue'
import './style.css'
import { useGlobalStore } from './stores/global'

const app = createApp(App)
const pinia = createPinia()

// 注册Pinia
app.use(pinia)

// 注册Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册路由
app.use(router)

// 初始化全局状态
const globalStore = useGlobalStore()
globalStore.initGlobalState()
globalStore.restoreStates()

app.mount('#app')
`
}

// 生成App.vue
function generateAppVue(): string {
  return `<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 应用根组件
</script>

<style>
#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
`
}

// 生成全局状态管理
function generateGlobalStore(): string {
  return `import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useGlobalStore = defineStore('global', () => {
  // 全局状态
  const currentRoute = ref('/')
  const windowStates = ref<Record<string, any>>({})
  const isDarkMode = ref(false)

  // 初始化全局状态
  const initGlobalState = () => {
    try {
      const saved = localStorage.getItem('easy-window-global-state')
      if (saved) {
        const globalState = JSON.parse(saved)
        isDarkMode.value = globalState.isDarkMode || false
      }
    } catch (error) {
      console.warn('恢复全局状态失败:', error)
    }
  }

  // 保存全局状态
  const saveGlobalState = () => {
    try {
      const globalState = {
        isDarkMode: isDarkMode.value
      }
      localStorage.setItem('easy-window-global-state', JSON.stringify(globalState))
    } catch (error) {
      console.warn('保存全局状态失败:', error)
    }
  }

  // 切换暗黑模式
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    saveGlobalState()

    // 通知所有窗口更新暗黑模式
    window.dispatchEvent(new CustomEvent('darkModeChanged', {
      detail: { isDarkMode: isDarkMode.value }
    }))
  }

  // 保存窗口状态
  const saveWindowState = (windowId: string, state: any) => {
    windowStates.value[windowId] = state
    localStorage.setItem('easy-window-window-states', JSON.stringify(windowStates.value))
  }

  // 获取窗口状态
  const getWindowState = (windowId: string) => {
    return windowStates.value[windowId] || {}
  }

  // 恢复所有窗口状态
  const restoreStates = () => {
    try {
      const saved = localStorage.getItem('easy-window-window-states')
      if (saved) {
        windowStates.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('恢复窗口状态失败:', error)
    }
  }

  // 清除所有状态
  const clearStates = () => {
    windowStates.value = {}
    isDarkMode.value = false
    localStorage.removeItem('easy-window-window-states')
    localStorage.removeItem('easy-window-global-state')
  }

  return {
    currentRoute,
    windowStates,
    isDarkMode,
    initGlobalState,
    saveGlobalState,
    toggleDarkMode,
    saveWindowState,
    getWindowState,
    restoreStates,
    clearStates
  }
})
`
}

// 生成全局样式
function generateGlobalStyles(): string {
  return `/* 全局样式重置 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  overflow: hidden;
}

* {
  box-sizing: border-box;
}

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 防止组件触发原生拖拽行为 */
* {
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

/* 允许输入框文本选择 */
.el-input__inner,
.el-textarea__inner {
  user-select: text !important;
}

/* 组件基础样式 */
.component-item {
  position: absolute;
  transition: all 0.2s ease;
}

/* 暗黑模式支持 */
.dark-mode {
  background: #1a1a1a;
  color: #ffffff;
}
`
}

// 生成README.md
function generateReadme(projectName: string): string {
  return `# ${projectName}

这是一个由 Easy Window 可视化设计器生成的 Vue3 + Element Plus 项目。

## 项目结构

\`\`\`
${projectName}/
├── src/
│   ├── views/          # 窗口组件
│   ├── components/     # 公共组件
│   ├── router/         # 路由配置
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   └── style.css       # 全局样式
├── public/             # 静态资源
├── package.json        # 项目配置
├── vite.config.ts      # Vite 配置
├── tsconfig.json       # TypeScript 配置
└── README.md           # 项目说明
\`\`\`

## 开发

\`\`\`bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
\`\`\`

## 技术栈

- Vue 3
- TypeScript
- Element Plus
- Vue Router
- Vite

## 说明

此项目由 Easy Window 可视化设计器自动生成，包含完整的项目结构和配置文件。
您可以直接运行 \`npm install\` 和 \`npm run dev\` 来启动项目。
`
}

// 生成.gitignore
function generateGitignore(): string {
  return `# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
`
}

// 生成路由配置
function generateRouterConfig(windows: WindowData[]): string {
  const routes = windows.map(window => {
    // 使用窗口的route属性，如果没有则使用窗口名称
    const routePath = window.route || window.name.toLowerCase()
    return `  {
    path: '/${routePath}',
    name: '${window.name}',
    component: () => import('@/views/${window.name}.vue'),
    meta: {
      title: '${window.titleBarConfig?.title || window.name}'
    }
  }`
  }).join(',\n')

  // 使用第一个窗口的route作为默认路由
  const defaultRoute = windows[0]?.route || windows[0]?.name.toLowerCase() || 'home'

  return `import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/${defaultRoute}'
  },
${routes}
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 设置页面标题和状态管理
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }

  // 保存当前路由到本地存储，用于刷新后恢复
  if (to.path !== '/') {
    localStorage.setItem('easy-window-last-route', to.path)
  }

  next()
})

// 应用启动时恢复上次访问的路由
router.afterEach(() => {
  // 在首次加载时恢复上次的路由
  if (router.currentRoute.value.path === '/') {
    const lastRoute = localStorage.getItem('easy-window-last-route')
    if (lastRoute && lastRoute !== '/') {
      router.replace(lastRoute)
    }
  }
})

export default router
`
}

// 生成窗口组件
function generateWindowComponent(window: WindowData): string {
  const components = window.components.map(comp => generateComponentCode(comp)).join('\n    ')

  const titleBarHtml = window.windowType === 'normal' ? `
        <!-- 标题栏 -->
        <WindowTitleBar
          :title="'${window.titleBarConfig?.title || window.name}'"
          :show-logo="${window.titleBarConfig?.showLogo || false}"
          :logo-path="'${window.titleBarConfig?.logoPath || ''}'"
          :show-dark-toggle="${window.titleBarConfig?.showDarkToggle || false}"
          :show-minimize="${window.titleBarConfig?.showMinimize || false}"
          :show-maximize="${window.titleBarConfig?.showMaximize || false}"
          :show-close="${window.titleBarConfig?.showClose || false}"
          :is-dark-mode="isDarkMode"
          @dark-toggle="toggleDarkMode"
          @minimize="minimize"
          @maximize="maximize"
          @close="closeWindow"
        />` : ''

  return `<template>
  <div class="preview-window" :class="{ 'window-dark-mode': isDarkMode }">
    <div class="window-container" :style="windowStyle">
      ${titleBarHtml}

      <!-- 窗口内容区域 -->
      <div class="window-content" :style="contentStyle">
        ${components}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import WindowTitleBar from '@/components/WindowTitleBar.vue'

// 状态持久化键名
const STORAGE_KEY = 'easy-window-${window.id}-state'
const GLOBAL_STORAGE_KEY = 'easy-window-global-state'

// 默认状态
const defaultState = {
  inputValue: '',
  selectValue: '',
  textareaValue: '',
  checkboxValue: false,
  radioValue: '',
  switchValue: false,
  sliderValue: 0,
  dateValue: null,
  timeValue: null,
  rateValue: 0,
  colorValue: '#409EFF',
  tableData: [
    { date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄' },
    { date: '2016-05-04', name: '王小虎', address: '上海市普陀区金沙江路 1517 弄' },
    { date: '2016-05-01', name: '王小虎', address: '上海市普陀区金沙江路 1519 弄' }
  ]
}

// 全局状态（所有窗口共享）
const defaultGlobalState = {
  isDarkMode: ${window.titleBarConfig?.isDarkMode || false}
}

// 从本地存储恢复窗口状态
const loadState = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY)
    return saved ? { ...defaultState, ...JSON.parse(saved) } : defaultState
  } catch (error) {
    console.warn('恢复窗口状态失败:', error)
    return defaultState
  }
}

// 从本地存储恢复全局状态
const loadGlobalState = () => {
  try {
    const saved = localStorage.getItem(GLOBAL_STORAGE_KEY)
    return saved ? { ...defaultGlobalState, ...JSON.parse(saved) } : defaultGlobalState
  } catch (error) {
    console.warn('恢复全局状态失败:', error)
    return defaultGlobalState
  }
}

// 保存窗口状态到本地存储
const saveState = () => {
  try {
    const state = {
      inputValue: inputValue.value,
      selectValue: selectValue.value,
      textareaValue: textareaValue.value,
      checkboxValue: checkboxValue.value,
      radioValue: radioValue.value,
      switchValue: switchValue.value,
      sliderValue: sliderValue.value,
      dateValue: dateValue.value,
      timeValue: timeValue.value,
      rateValue: rateValue.value,
      colorValue: colorValue.value
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
  } catch (error) {
    console.warn('保存窗口状态失败:', error)
  }
}

// 保存全局状态到本地存储
const saveGlobalState = () => {
  try {
    const globalState = {
      isDarkMode: isDarkMode.value
    }
    localStorage.setItem(GLOBAL_STORAGE_KEY, JSON.stringify(globalState))

    // 通知其他窗口更新暗黑模式状态
    window.dispatchEvent(new CustomEvent('darkModeChanged', {
      detail: { isDarkMode: isDarkMode.value }
    }))
  } catch (error) {
    console.warn('保存全局状态失败:', error)
  }
}

// 初始化状态
const initialState = loadState()
const initialGlobalState = loadGlobalState()

// 全局响应式数据（所有窗口共享）
const isDarkMode = ref(initialGlobalState.isDarkMode)

// 窗口独立的组件数据
const inputValue = ref(initialState.inputValue)
const selectValue = ref(initialState.selectValue)
const textareaValue = ref(initialState.textareaValue)
const checkboxValue = ref(initialState.checkboxValue)
const radioValue = ref(initialState.radioValue)
const switchValue = ref(initialState.switchValue)
const sliderValue = ref(initialState.sliderValue)
const dateValue = ref(initialState.dateValue)
const timeValue = ref(initialState.timeValue)
const rateValue = ref(initialState.rateValue)
const colorValue = ref(initialState.colorValue)

// 计算属性 - 完全复制预览窗口的样式计算
const windowStyle = computed(() => ({
  width: '${window.windowConfig.width}px',
  height: '${window.windowConfig.height}px',
  backgroundColor: '${window.windowConfig.backgroundColor}',
  border: '${window.windowConfig.border}',
  borderRadius: '${window.windowConfig.borderRadius}px',
  boxShadow: '${window.windowConfig.boxShadow}',
  position: 'relative',
  overflow: 'hidden'
}))

const contentStyle = computed(() => ({
  width: '100%',
  height: '${window.windowType === 'normal' ? 'calc(100% - ' + (window.titleBarConfig?.height || 36) + 'px)' : '100%'}',
  position: 'relative',
  backgroundColor: 'transparent'
}))

// 方法
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  saveGlobalState()
  ElMessage.success(\`已切换到\${isDarkMode.value ? '暗黑' : '亮色'}模式\`)
}

// 监听窗口状态变化并自动保存
watch([
  inputValue,
  selectValue,
  textareaValue,
  checkboxValue,
  radioValue,
  switchValue,
  sliderValue,
  dateValue,
  timeValue,
  rateValue,
  colorValue
], () => {
  saveState()
}, { deep: true })

// 监听全局状态变化并自动保存
watch(isDarkMode, () => {
  saveGlobalState()
}, { deep: true })

// 监听其他窗口的暗黑模式变化
const handleDarkModeChange = (event: CustomEvent) => {
  if (event.detail.isDarkMode !== isDarkMode.value) {
    isDarkMode.value = event.detail.isDarkMode
  }
}

// 页面加载时监听全局事件
onMounted(() => {
  window.addEventListener('darkModeChanged', handleDarkModeChange as EventListener)
})

// 页面卸载前保存状态并清理事件监听
onBeforeUnmount(() => {
  saveState()
  saveGlobalState()
  window.removeEventListener('darkModeChanged', handleDarkModeChange as EventListener)
})

const minimize = () => {
  ElMessage.info('最小化功能')
}

const maximize = () => {
  ElMessage.info('最大化功能')
}

const closeWindow = () => {
  ElMessage.info('关闭窗口功能')
}

const handleButtonClick = () => {
  ElMessage.success('按钮被点击')
}

// 处理组件事件绑定
const handleComponentEvent = (cmd: string, componentId: string, eventData?: any) => {
  console.log('组件事件触发:', { cmd, componentId, eventData })

  // 这里可以添加与易语言通信的逻辑
  // 例如：发送消息到易语言程序
  if (window.external && window.external.notify) {
    // 如果在易语言WebBrowser控件中运行
    window.external.notify(JSON.stringify({
      cmd,
      componentId,
      data: eventData
    }))
  } else {
    // 开发环境下的提示
    ElMessage.info(\`事件触发: \${cmd}\`)
  }
}
</script>

<style scoped>
/* 完全复制预览窗口的样式,注意:我修改了样式,我不需要它居中显示！！！ */
.preview-window {
  width: 100vw;
  height: 100vh;
  display: flex;
  background: #f5f5f5;
  transition: all 0.3s ease;
  overflow: hidden;
}



.window-container {
  position: relative;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.window-content {
  position: relative;
  background: transparent;
  transition: all 0.3s ease;
}

.preview-window.window-dark-mode .window-content {
  background: #1f1f1f;
}

.preview-window.window-dark-mode .window-container {
  background: #1f1f1f !important;
  border-color: #404040 !important;
}

/* 完全复制预览组件的样式 */
.preview-component {
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.component-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统一 input 拉伸样式 */
.component-inner :deep(.el-input),
.component-inner :deep(.el-input__wrapper),
.component-inner :deep(.el-input__inner) {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}

/* 图片组件样式 */
.component-inner :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  display: block !important;
  flex: none !important;
  position: relative !important;
}

.component-inner :deep(.el-image__inner) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  object-fit: inherit !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* 图片组件容器特殊处理 */
.component-inner.image-container {
  display: block !important;
  position: relative !important;
}

/* 暗黑模式下的组件样式 */
.preview-component.window-dark-mode .component-inner :deep(.el-input) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
  --el-input-placeholder-color: #8c8c8c !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-button) {
  --el-button-bg-color: #404040 !important;
  --el-button-border-color: #606060 !important;
  --el-button-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-button--primary) {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-select) {
  --el-select-input-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-select .el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-textarea) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-textarea__inner) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner span {
  color: #ffffff !important;
}

/* 日期时间选择器暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-date-editor) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-date-editor .el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-date-editor .el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

/* 评分组件暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-rate) {
  --el-rate-void-color: #404040 !important;
  --el-rate-fill-color: #F7BA2A !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-rate__text) {
  color: #ffffff !important;
}

/* 颜色选择器暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-color-picker) {
  --el-color-picker-border-color: #404040 !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-color-picker__trigger) {
  border-color: #404040 !important;
}

/* 滑块组件暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-slider) {
  --el-slider-runway-bg-color: #404040 !important;
  --el-slider-main-bg-color: #409eff !important;
  --el-slider-button-bg-color: #409eff !important;
}

/* 开关组件暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-switch) {
  --el-switch-off-color: #404040 !important;
  --el-switch-on-color: #409eff !important;
}

/* 复选框和单选框暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-checkbox) {
  --el-checkbox-bg-color: #2c2c2c !important;
  --el-checkbox-border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-radio) {
  --el-radio-bg-color: #2c2c2c !important;
  --el-radio-border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-checkbox__label),
.preview-component.window-dark-mode .component-inner :deep(.el-radio__label) {
  color: #ffffff !important;
}

.window-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: ${window.titleBarConfig?.height || 36}px;
  background: transparent;
  user-select: none;
}

.window-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  color: #ffffff;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-logo {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.window-title {
  font-weight: 600;
  font-size: 14px;
}

.title-bar-controls {
  display: flex;
  gap: 2px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: var(--el-fill-color-light);
}

.close-btn:hover {
  background: var(--el-color-danger);
  color: #ffffff;
}

.window-content {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  transition: all 0.3s ease;
}

.window-content.window-dark-mode {
  background: #1f1f1f;
  color: #ffffff;
}

/* 组件样式 */
.component-item {
  position: absolute;
  transition: all 0.2s ease;
}

/* 输入框组件样式 */
.component-item.el-input,
.component-item .el-input,
.component-item .el-input__wrapper,
.component-item .el-input__inner {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
}

/* 按钮组件样式 */
.component-item.el-button {
  width: 100% !important;
  height: 100% !important;
}

/* 图片组件样式 */
.component-item.el-image {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

/* 选择器组件样式 */
.component-item.el-select {
  width: 100% !important;
}

.component-item .el-select .el-input__wrapper {
  width: 100% !important;
  height: 100% !important;
}

/* 文本域组件样式 */
.component-item .el-textarea {
  width: 100% !important;
  height: 100% !important;
}

.component-item .el-textarea__inner {
  width: 100% !important;
  height: 100% !important;
  resize: none !important;
}

/* 暗黑模式下的组件样式 */
.window-content.window-dark-mode .component-item .el-input {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
  --el-input-placeholder-color: #8c8c8c !important;
}

.window-content.window-dark-mode .component-item .el-input__wrapper {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-input__inner {
  color: #ffffff !important;
  background-color: transparent !important;
}

.window-content.window-dark-mode .component-item .el-button {
  --el-button-bg-color: #404040 !important;
  --el-button-border-color: #606060 !important;
  --el-button-text-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-button--primary {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-select {
  --el-select-input-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-select .el-input__wrapper {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.window-content.window-dark-mode .component-item .el-textarea {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-textarea__inner {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.window-content.window-dark-mode .component-item span {
  color: #ffffff !important;
}
</style>
`
}

// 生成单个组件代码 - 完全复制预览组件的结构
function generateComponentCode(component: ComponentData): string {
  const componentStyle = `
    position: absolute;
    left: ${component.x}px;
    top: ${component.y}px;
    width: ${component.width}px;
    height: ${component.height}px;
  `

  return `<div
      class="preview-component"
      :class="{ 'window-dark-mode': isDarkMode }"
      style="${componentStyle}"
    >
      <div class="component-inner" :class="{ 'image-container': '${component.type}' === 'image' }">
        ${generateInnerComponent(component)}
      </div>
    </div>`
}

// 生成组件内部内容
function generateInnerComponent(component: ComponentData): string {
  switch (component.type) {
    case 'input':
      const inputChangeHandler = component.events?.cmd ? `@change="handleComponentEvent('${component.events.cmd}', '${component.id}', $event)"` : ''
      return `<el-input
        v-model="inputValue"
        type="${component.props?.inputType || 'text'}"
        placeholder="${component.props?.placeholder || '请输入内容'}"
        :disabled="${component.props?.disabled || false}"
        :readonly="${component.props?.readonly || false}"
        :clearable="${component.props?.clearable || false}"
        :show-password="${component.props?.showPassword || false}"
        :show-word-limit="${component.props?.showWordLimit || false}"
        :maxlength="${component.props?.maxlength || undefined}"
        :minlength="${component.props?.minlength || undefined}"
        size="${component.props?.size || 'default'}"
        autocomplete="${component.props?.autocomplete || 'off'}"
        ${inputChangeHandler}
      />`

    case 'button':
      const buttonClickHandler = component.events?.cmd ? `handleComponentEvent('${component.events.cmd}', '${component.id}')` : 'handleButtonClick'
      return `<el-button
        style="width:100%;height:100%;"
        type="${component.props?.type || 'primary'}"
        size="${component.props?.size || 'default'}"
        :disabled="${component.props?.disabled || false}"
        :plain="${component.props?.plain || false}"
        :round="${component.props?.round || false}"
        :circle="${component.props?.circle || false}"
        :loading="${component.props?.loading || false}"
        :autofocus="${component.props?.autofocus || false}"
        native-type="${component.props?.nativeType || 'button'}"
        @click="${buttonClickHandler}"
      >
        ${component.props?.text || '按钮'}
      </el-button>`

    case 'text':
      return `<el-text
        type="${component.props?.type || 'primary'}"
        size="${component.props?.size || 'default'}"
        :truncated="${component.props?.truncated || false}"
        tag="${component.props?.tag || 'span'}"
        style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;"
      >
        ${component.props?.content || '文本内容'}
      </el-text>`

    case 'image':
      return `<el-image
        src="${component.props?.src || 'https://via.placeholder.com/150'}"
        alt="${component.props?.alt || '图片'}"
        fit="${component.props?.fit || 'cover'}"
        loading="${component.props?.loading || 'eager'}"
        :lazy="${component.props?.lazy || false}"
        draggable="false"
        style="width: 100%; height: 100%; display: block;"
      />`

    case 'select':
      const options = component.props?.options || []
      const optionElements = options.map((opt: any) =>
        `<el-option label="${opt.label}" value="${opt.value}" ${opt.disabled ? ':disabled="true"' : ''} />`
      ).join('\n        ')

      return `<el-select
        v-model="selectValue"
        placeholder="${component.props?.placeholder || '请选择'}"
        :disabled="${component.props?.disabled || false}"
        :clearable="${component.props?.clearable || false}"
        size="${component.props?.size || 'default'}"
        style="width:100%;"
      >
        ${optionElements}
      </el-select>`

    case 'textarea':
      return `<el-input
        v-model="textareaValue"
        type="textarea"
        placeholder="${component.props?.placeholder || '请输入内容'}"
        :rows="${component.props?.rows || 3}"
        :disabled="${component.props?.disabled || false}"
        style="width:100%;height:100%;"
      />`

    case 'checkbox':
      return `<el-checkbox
        v-model="checkboxValue"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
      >
        ${component.props?.label || '复选框'}
      </el-checkbox>`

    case 'radio':
      return `<el-radio
        v-model="radioValue"
        label="${component.props?.value || 'option1'}"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
      >
        ${component.props?.label || '单选框'}
      </el-radio>`

    case 'switch':
      return `<el-switch
        v-model="switchValue"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
        active-text="${component.props?.activeText || ''}"
        inactive-text="${component.props?.inactiveText || ''}"
      />`

    case 'slider':
      return `<el-slider
        v-model="sliderValue"
        :min="${component.props?.min || 0}"
        :max="${component.props?.max || 100}"
        :step="${component.props?.step || 1}"
        :disabled="${component.props?.disabled || false}"
        :show-input="${component.props?.showInput || false}"
        style="width:100%;"
      />`

    case 'date-picker':
      return `<el-date-picker
        v-model="dateValue"
        type="${component.props?.type || 'date'}"
        placeholder="${component.props?.placeholder || '选择日期'}"
        :disabled="${component.props?.disabled || false}"
        :clearable="${component.props?.clearable || true}"
        :readonly="${component.props?.readonly || false}"
        size="${component.props?.size || 'default'}"
        format="${component.props?.format || 'YYYY-MM-DD'}"
        value-format="${component.props?.valueFormat || 'YYYY-MM-DD'}"
        style="width:100%;"
      />`

    case 'time-picker':
      return `<el-time-picker
        v-model="timeValue"
        placeholder="${component.props?.placeholder || '选择时间'}"
        :disabled="${component.props?.disabled || false}"
        :clearable="${component.props?.clearable || true}"
        :readonly="${component.props?.readonly || false}"
        size="${component.props?.size || 'default'}"
        format="${component.props?.format || 'HH:mm:ss'}"
        value-format="${component.props?.valueFormat || 'HH:mm:ss'}"
        style="width:100%;"
      />`

    case 'rate':
      return `<el-rate
        v-model="rateValue"
        :max="${component.props?.max || 5}"
        :disabled="${component.props?.disabled || false}"
        :allow-half="${component.props?.allowHalf || false}"
        :low-threshold="${component.props?.lowThreshold || 2}"
        :high-threshold="${component.props?.highThreshold || 4}"
        void-color="${component.props?.voidColor || '#C6D1DE'}"
        disabled-void-color="${component.props?.disabledVoidColor || '#EFF2F7'}"
        :show-text="${component.props?.showText || false}"
        :show-score="${component.props?.showScore || false}"
        text-color="${component.props?.textColor || '#1F2D3D'}"
        size="${component.props?.size || 'default'}"
      />`

    case 'color-picker':
      return `<el-color-picker
        v-model="colorValue"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
        :show-alpha="${component.props?.showAlpha || false}"
        color-format="${component.props?.colorFormat || 'hex'}"
      />`

    case 'card':
      const cardChildren = component.children ? component.children.map(child => generateComponentCode(child)).join('\n        ') : ''
      return `<el-card
        header="${component.props?.header || '卡片标题'}"
        shadow="${component.props?.shadow || 'always'}"
        style="width: 100%; height: 100%;"
      >
        <div style="position: relative; width: 100%; height: 100%;">
          ${cardChildren || `<div style="padding: 20px; text-align: center; color: #999;">${component.props?.content || '卡片内容'}</div>`}
        </div>
      </el-card>`

    case 'carousel':
      const carouselItems = component.props?.items || []
      const carouselItemsCode = carouselItems.map((item: any, index: number) =>
        `<el-carousel-item style="background-color: ${item.color || '#99a9bf'}">
          <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 14px;">
            ${item.content || `轮播项 ${index + 1}`}
          </div>
        </el-carousel-item>`
      ).join('\n        ')
      return `<el-carousel
        height="${component.props?.height || '150px'}"
        trigger="${component.props?.trigger || 'hover'}"
        :autoplay="${component.props?.autoplay !== false}"
        :interval="${component.props?.interval || 4000}"
        indicator-position="${component.props?.indicator ? 'outside' : 'none'}"
        arrow="${component.props?.arrow || 'hover'}"
        type="${component.props?.type || ''}"
        :loop="${component.props?.loop !== false}"
        direction="${component.props?.direction || 'horizontal'}"
        style="width: 100%; height: 100%;"
      >
        ${carouselItemsCode}
      </el-carousel>`

    case 'tag':
      return `<el-tag
        type="${component.props?.type || ''}"
        :closable="${component.props?.closable || false}"
        :disable-transitions="${component.props?.disableTransitions || false}"
        :hit="${component.props?.hit || false}"
        color="${component.props?.color || ''}"
        size="${component.props?.size || 'default'}"
        effect="${component.props?.effect || 'light'}"
        :round="${component.props?.round || false}"
      >
        ${component.props?.text || '标签'}
      </el-tag>`

    case 'tabs':
      const tabs = component.props?.tabs || []
      const tabPanesCode = tabs.map((tab: any) => {
        const tabChildren = component.children ?
          component.children.filter((child: any) =>
            child.props?.tabName === tab.name || (!child.props?.tabName && tab.name === tabs[0]?.name)
          ).map(child => generateComponentCode(child)).join('\n          ') : ''
        return `<el-tab-pane label="${tab.label}" name="${tab.name}">
          <div style="position: relative; width: 100%; height: 100%; min-height: 200px;">
            ${tabChildren || `<div style="padding: 20px; text-align: center; color: #999;">${tab.content || '标签页内容'}</div>`}
          </div>
        </el-tab-pane>`
      }).join('\n        ')
      return `<el-tabs
        model-value="${component.props?.activeTab || 'first'}"
        type="${component.props?.type || ''}"
        :closable="${component.props?.closable || false}"
        :addable="${component.props?.addable || false}"
        :editable="${component.props?.editable || false}"
        tab-position="${component.props?.tabPosition || 'top'}"
        :stretch="${component.props?.stretch || false}"
        style="width: 100%; height: 100%;"
      >
        ${tabPanesCode}
      </el-tabs>`

    case 'table':
      const columns = component.props?.columns || []
      const tableColumnsCode = columns.map((column: any) =>
        `<el-table-column
          prop="${column.prop}"
          label="${column.label}"
          width="${column.width || ''}"
          min-width="${column.minWidth || ''}"
          fixed="${column.fixed || false}"
          sortable="${column.sortable || false}"
          resizable="${column.resizable !== false}"
          show-overflow-tooltip="${column.showOverflowTooltip || false}"
          align="${column.align || 'left'}"
          header-align="${column.headerAlign || 'left'}"
        />`
      ).join('\n        ')
      return `<el-table
        :data="tableData"
        :stripe="${component.props?.stripe || false}"
        :border="${component.props?.border || false}"
        size="${component.props?.size || 'default'}"
        :fit="${component.props?.fit !== false}"
        :show-header="${component.props?.showHeader !== false}"
        :highlight-current-row="${component.props?.highlightCurrentRow || false}"
        empty-text="${component.props?.emptyText || '暂无数据'}"
        style="width: 100%; height: 100%;"
      >
        ${tableColumnsCode}
      </el-table>`

    default:
      return `<div style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;">
        <el-tag type="info">${component.type || '未知组件'}</el-tag>
      </div>`
  }
}

// 生成WindowTitleBar组件
function generateWindowTitleBarComponent(): string {
  return `<template>
  <div class="window-title-bar" :class="{ 'dark-mode': isDarkMode }">
    <div class="title-bar-left">
      <el-icon v-if="showLogo && !logoPath" :size="18"><Monitor /></el-icon>
      <img v-if="showLogo && logoPath" :src="logoPath" alt="应用图标" class="app-logo" />
      <span class="window-title">{{ title }}</span>
    </div>
    <div class="title-bar-controls">
      <button v-if="showDarkToggle" class="control-btn" @click="$emit('dark-toggle')">
        <el-icon :size="14"><component :is="isDarkMode ? 'Sunny' : 'Moon'" /></el-icon>
      </button>
      <button v-if="showMinimize" class="control-btn" @click="$emit('minimize')">
        <el-icon :size="14"><Minus /></el-icon>
      </button>
      <button v-if="showMaximize" class="control-btn" @click="$emit('maximize')">
        <el-icon :size="14"><FullScreen /></el-icon>
      </button>
      <button v-if="showClose" class="control-btn close-btn" @click="$emit('close')">
        <el-icon :size="14"><Close /></el-icon>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Close, FullScreen, Minus, Moon, Sunny, Monitor } from '@element-plus/icons-vue'

defineProps<{
  title: string
  showLogo: boolean
  logoPath: string
  showDarkToggle: boolean
  showMinimize: boolean
  showMaximize: boolean
  showClose: boolean
  isDarkMode: boolean
}>()

defineEmits<{
  'dark-toggle': []
  'minimize': []
  'maximize': []
  'close': []
}>()
</script>

<style scoped>
.window-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: 36px;
  background: transparent;
  user-select: none;
}

.window-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  color: #ffffff;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-logo {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.window-title {
  font-weight: 600;
  font-size: 14px;
}

.title-bar-controls {
  display: flex;
  gap: 2px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn {
  color: #606266;
}

.control-btn:hover {
  background: var(--el-fill-color-light);
}

.close-btn:hover {
  background: var(--el-color-danger);
  color: #ffffff;
}

/* 暗黑模式下的按钮颜色 */
.window-title-bar.dark-mode .control-btn {
  color: #ffffff;
}

.window-title-bar.dark-mode .control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.window-title-bar.dark-mode .close-btn:hover {
  background: var(--el-color-danger);
  color: #ffffff;
}
</style>
`
}
